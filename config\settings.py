"""配置管理"""

import os
import logging
from typing import Dict, Any, List
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings:
    """应用配置类"""

    def __init__(self):
        self.validate_environment()

    # AI模型配置
    API_KEY: str = os.getenv("API_KEY", "")
    API_PROXY: str = os.getenv("API_PROXY", "https://api.siliconflow.cn/v1")
    AI_MODEL: str = os.getenv("AI_MODEL", "Qwen/Qwen3-8B")

    # 搜索工具配置
    TAVILY_API_KEY: str = os.getenv("TAVILY_API_KEY", "")

    # Jina Reader配置
    JINA_READER_BASE_URL: str = os.getenv("JINA_READER_BASE_URL", "https://r.jina.ai")

    # 应用配置
    MAX_SEARCH_RESULTS: int = int(os.getenv("MAX_SEARCH_RESULTS", "10"))
    RESEARCH_TIMEOUT: int = int(os.getenv("RESEARCH_TIMEOUT", "300"))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 会话配置
    SESSION_TIMEOUT_HOURS: int = int(os.getenv("SESSION_TIMEOUT_HOURS", "24"))
    MAX_CONCURRENT_SESSIONS: int = int(os.getenv("MAX_CONCURRENT_SESSIONS", "100"))

    # 文件上传配置
    MAX_FILE_SIZE_MB: int = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
    MAX_FILES_PER_SESSION: int = int(os.getenv("MAX_FILES_PER_SESSION", "20"))
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "storage/uploads")

    # 安全配置
    ALLOWED_ORIGINS: List[str] = os.getenv(
        "ALLOWED_ORIGINS", "http://localhost:7860,http://127.0.0.1:7860"
    ).split(",")
    ENABLE_AUTH: bool = os.getenv("ENABLE_AUTH", "false").lower() == "true"

    @classmethod
    def validate_environment(cls) -> bool:
        """验证必需的环境变量"""
        required_vars = ["API_KEY", "TAVILY_API_KEY"]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")

        return True

    @classmethod
    def get_api_config(cls) -> Dict[str, Any]:
        """获取API配置"""
        return {
            "api_key": cls.API_KEY,
            "api_proxy": cls.API_PROXY,
            "ai_model": cls.AI_MODEL,
            "tavily_key": cls.TAVILY_API_KEY,
            "jina_base_url": cls.JINA_READER_BASE_URL,
        }

    @classmethod
    def setup_logging(cls):
        """设置日志配置"""
        # 创建logs目录
        os.makedirs("logs", exist_ok=True)

        # 配置日志
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        log_level = getattr(logging, cls.LOG_LEVEL.upper(), logging.INFO)

        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(
                    f"logs/app_{datetime.now().strftime('%Y%m%d')}.log"
                ),
                logging.StreamHandler(),
            ],
        )


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
