"""Gradio深度研究工具主应用"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from ui.interface import create_app


def main():
    """主函数"""
    try:
        # 设置日志
        settings.setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("启动深度研究工具...")
        logger.info(f"AI模型: {settings.AI_MODEL}")
        logger.info(f"API代理: {settings.API_PROXY}")
        
        # 验证环境配置
        settings.validate_environment()
        logger.info("环境配置验证通过")
        
        # 创建必要的目录
        os.makedirs("logs", exist_ok=True)
        os.makedirs("storage", exist_ok=True)
        os.makedirs("storage/uploads", exist_ok=True)
        os.makedirs("storage/sessions", exist_ok=True)
        
        # 创建并启动应用
        app = create_app()
        
        logger.info("正在启动Gradio应用...")
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=True,
            show_error=True
        )
        
    except Exception as e:
        print(f"应用启动失败: {str(e)}")
        logging.error(f"应用启动失败: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
