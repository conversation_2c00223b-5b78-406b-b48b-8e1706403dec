"""数据模型定义"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime


class ResearchPhase(Enum):
    """研究阶段枚举"""
    TOPIC_INPUT = "topic_input"
    THINKING = "thinking"
    PLANNING = "planning"
    COLLECTION = "collection"
    DEEP_RESEARCH = "deep_research"
    FINAL_REPORT = "final_report"


class ThinkingResult(BaseModel):
    """思考结果数据模型"""
    topic: str
    analysis: str
    questions: List[str] = Field(default_factory=list)
    insights: List[str] = Field(default_factory=list)
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class ResearchPlan(BaseModel):
    """研究计划数据模型"""
    topic: str
    objectives: List[str]
    methodology: str
    key_areas: List[str]
    timeline: Optional[str] = None
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())


class DocRef(BaseModel):
    """文档引用数据模型"""
    title: str
    url: Optional[str] = None
    content: str
    source: str = Field(..., description="来源类型: web, local, api")
    relevance_score: float = Field(default=0.0, ge=0.0, le=1.0)
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class SessionData(BaseModel):
    """会话数据模型"""
    session_id: str
    current_phase: ResearchPhase
    completed_phases: List[ResearchPhase] = Field(default_factory=list)
    step_data: Dict[str, Any] = Field(default_factory=dict)
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())


class APIError(Exception):
    """API错误异常"""
    def __init__(self, service: str, error_code: str, message: str):
        self.service = service
        self.error_code = error_code
        self.message = message
        super().__init__(f"{service} API Error [{error_code}]: {message}")


class StepExecutionError(Exception):
    """步骤执行异常"""
    def __init__(self, step_name: str, error_type: str, message: str, retry_count: int = 0):
        self.step_name = step_name
        self.error_type = error_type
        self.message = message
        self.retry_count = retry_count
        super().__init__(f"Step {step_name} failed: {message}")


# 错误码定义
ERROR_CODES = {
    "RATE_LIMIT": "API调用频率超限",
    "AUTH_FAILED": "API密钥验证失败",
    "TIMEOUT": "请求超时",
    "INVALID_RESPONSE": "响应格式错误",
    "SERVICE_UNAVAILABLE": "服务不可用",
    "QUOTA_EXCEEDED": "配额已用完",
    "INVALID_INPUT": "输入参数无效"
}


# 重试配置
RETRY_CONFIG = {
    "max_retries": 3,
    "backoff_factor": 2.0,
    "retry_on_errors": ["TIMEOUT", "SERVICE_UNAVAILABLE", "RATE_LIMIT"],
    "timeout_seconds": 30
}


# 文件配置
FILE_CONFIG = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "supported_formats": {
        ".pdf": "application/pdf",
        ".txt": "text/plain",
        ".md": "text/markdown",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    },
    "max_files_per_session": 20
}
