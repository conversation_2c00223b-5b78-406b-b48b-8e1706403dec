"""UI组件定义"""

import gradio as gr
from typing import <PERSON>ple, Any
from config.models import ResearchPhase


def create_step_indicator(current_step: int = 0, completed_steps: list = None) -> str:
    """创建步骤指示器HTML"""
    if completed_steps is None:
        completed_steps = []
    
    steps = [
        ("主题输入", "topic_input"),
        ("AI思考", "thinking"), 
        ("制定计划", "planning"),
        ("信息收集", "collection"),
        ("深度研究", "deep_research"),
        ("生成报告", "final_report")
    ]
    
    html_parts = []
    for i, (name, step_id) in enumerate(steps):
        if i < len(completed_steps) and completed_steps[i]:
            css_class = "step-completed"
            icon = "✓"
        elif i == current_step:
            css_class = "step-current"
            icon = "▶"
        else:
            css_class = "step-pending"
            icon = str(i + 1)
        
        html_parts.append(f'''
            <div class="step-item {css_class}">
                <div class="step-icon">{icon}</div>
                <div class="step-name">{name}</div>
            </div>
        ''')
    
    return f'''
    <div class="step-indicator">
        {" ".join(html_parts)}
    </div>
    <style>
    .step-indicator {{
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }}
    .step-item {{
        flex: 1;
        text-align: center;
        padding: 10px;
        border-radius: 8px;
        margin: 0 5px;
    }}
    .step-completed {{
        background: #4caf50;
        color: white;
    }}
    .step-current {{
        background: #2196f3;
        color: white;
    }}
    .step-pending {{
        background: #f5f5f5;
        color: #999;
    }}
    .step-icon {{
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 4px;
    }}
    .step-name {{
        font-size: 12px;
        font-weight: 500;
    }}
    </style>
    '''


def create_topic_input_step() -> Tuple[Any, ...]:
    """创建主题输入步骤"""
    with gr.Column():
        gr.Markdown("## 📝 第一步：确定研究主题")
        
        topic_input = gr.Textbox(
            label="请输入您要研究的主题",
            placeholder="例如：人工智能在医疗领域的应用现状与发展趋势",
            lines=3,
            elem_id="topic_input"
        )
        
        topic_examples = gr.Examples(
            examples=[
                ["区块链技术在供应链管理中的应用"],
                ["可持续能源发展的挑战与机遇"],
                ["远程工作对企业文化的影响"],
                ["量子计算的发展现状与未来前景"],
                ["人工智能伦理问题研究"]
            ],
            inputs=topic_input,
            label="示例主题"
        )
        
        topic_status = gr.HTML(value="", visible=False)
    
    return topic_input, topic_examples, topic_status


def create_thinking_step() -> Tuple[Any, ...]:
    """创建思考步骤"""
    with gr.Column():
        gr.Markdown("## 🤔 第二步：AI深度思考")
        
        # 思考过程展示区域
        with gr.Accordion("思考过程", open=False) as thinking_accordion:
            thinking_display = gr.Markdown(
                value="",
                elem_classes=["thinking-stream"]
            )
        
        # 思考结果摘要
        thinking_summary = gr.Markdown(value="", visible=False)
        
        # AI提出的问题（如果有）
        ai_questions = gr.Markdown(value="", visible=False)
        
        user_answers = gr.Textbox(
            label="您的回答（可选）",
            placeholder="请回答AI提出的问题，或直接跳过进入下一步",
            lines=5,
            visible=False
        )
    
    return thinking_accordion, thinking_display, thinking_summary, ai_questions, user_answers


def create_planning_step() -> Tuple[Any, ...]:
    """创建计划步骤"""
    with gr.Column():
        gr.Markdown("## 📋 第三步：制定研究计划")
        
        research_plan = gr.Markdown(value="")
        
        plan_feedback = gr.Textbox(
            label="对研究计划的反馈（可选）",
            placeholder="如果您对研究计划有建议或修改意见，请在此输入",
            lines=3
        )
        
        serp_queries = gr.JSON(
            label="生成的搜索关键词",
            visible=False
        )
    
    return research_plan, plan_feedback, serp_queries


def create_navigation_controls() -> Tuple[Any, ...]:
    """创建导航控制组件"""
    with gr.Row():
        prev_btn = gr.Button(
            "← 上一步",
            variant="secondary",
            interactive=False,
            elem_id="btn_prev"
        )
        
        execute_btn = gr.Button(
            "执行当前步骤",
            variant="primary",
            elem_id="btn_execute"
        )
        
        next_btn = gr.Button(
            "下一步 →",
            variant="primary",
            interactive=False,
            visible=False,  # 使用自动跃迁时隐藏
            elem_id="btn_next"
        )
        
        redo_btn = gr.Button(
            "🔄 重做本步",
            variant="secondary",
            interactive=False,
            elem_id="btn_redo"
        )
    
    with gr.Row():
        auto_advance = gr.Checkbox(
            label="启用自动跃迁",
            value=True,
            info="步骤完成后自动进入下一步"
        )
        
        progress_display = gr.HTML(
            value="",
            visible=False,
            elem_classes=["progress-display"]
        )
    
    return prev_btn, execute_btn, next_btn, redo_btn, auto_advance, progress_display


def create_error_display(error_message: str = "") -> str:
    """创建错误显示HTML"""
    if not error_message:
        return ""
    
    return f"""
    <div class="error-container">
        <div class="error-header">
            <span class="error-icon">⚠️</span>
            <span class="error-title">执行失败</span>
        </div>
        <div class="error-message">
            {error_message}
        </div>
    </div>
    <style>
    .error-container {{
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 8px;
        padding: 16px;
        margin: 10px 0;
    }}
    .error-header {{
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }}
    .error-icon {{
        font-size: 20px;
        margin-right: 8px;
    }}
    .error-title {{
        font-weight: bold;
        color: #c53030;
    }}
    .error-message {{
        color: #744210;
        font-size: 14px;
        line-height: 1.5;
    }}
    </style>
    """


def create_success_display(message: str = "") -> str:
    """创建成功显示HTML"""
    if not message:
        return ""
    
    return f"""
    <div class="success-container">
        <div class="success-header">
            <span class="success-icon">✅</span>
            <span class="success-title">执行成功</span>
        </div>
        <div class="success-message">
            {message}
        </div>
    </div>
    <style>
    .success-container {{
        background: #f0fff4;
        border: 1px solid #9ae6b4;
        border-radius: 8px;
        padding: 16px;
        margin: 10px 0;
    }}
    .success-header {{
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }}
    .success-icon {{
        font-size: 20px;
        margin-right: 8px;
    }}
    .success-title {{
        font-weight: bold;
        color: #276749;
    }}
    .success-message {{
        color: #276749;
        font-size: 14px;
        line-height: 1.5;
    }}
    </style>
    """
