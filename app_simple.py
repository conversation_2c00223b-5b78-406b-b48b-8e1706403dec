"""简化版深度研究工具"""

import gradio as gr
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from core.ai_client import get_ai_client

# 设置日志
settings.setup_logging()
logger = logging.getLogger(__name__)

# 全局AI客户端
ai_client = get_ai_client()


def create_step_indicator(current_step: int, completed_steps: list) -> str:
    """创建步骤指示器"""
    steps = ["主题输入", "AI思考", "研究计划", "信息收集", "深度研究", "生成报告"]
    
    html_parts = []
    for i, name in enumerate(steps):
        if completed_steps[i]:
            css_class = "step-completed"
            icon = "✓"
        elif i == current_step:
            css_class = "step-current" 
            icon = "▶"
        else:
            css_class = "step-pending"
            icon = str(i + 1)
        
        html_parts.append(f'''
            <div class="step-item {css_class}">
                <div class="step-icon">{icon}</div>
                <div class="step-name">{name}</div>
            </div>
        ''')
    
    return f'''
    <div class="step-indicator">
        {"".join(html_parts)}
    </div>
    <style>
    .step-indicator {{
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }}
    .step-item {{
        flex: 1;
        text-align: center;
        padding: 10px;
        border-radius: 8px;
        margin: 0 5px;
    }}
    .step-completed {{ background: #4caf50; color: white; }}
    .step-current {{ background: #2196f3; color: white; }}
    .step-pending {{ background: #f5f5f5; color: #999; }}
    .step-icon {{ font-size: 18px; font-weight: bold; margin-bottom: 4px; }}
    .step-name {{ font-size: 12px; font-weight: 500; }}
    </style>
    '''


async def execute_topic_step(topic: str, current_step: int, completed_steps: list, step_data: dict, auto_advance: bool):
    """执行主题输入步骤"""
    if not topic.strip():
        return [
            gr.update(),  # current_step
            gr.update(),  # completed_steps  
            gr.update(),  # step_data
            gr.update(),  # step_indicator
            gr.update(value="❌ 请输入研究主题"),  # status
            gr.update(),  # thinking_result
            gr.update(),  # plan_result
            gr.update()   # tabs
        ]
    
    # 保存主题
    step_data["topic"] = topic.strip()
    completed_steps[0] = True
    
    # 如果启用自动跃迁，切换到下一步
    new_step = 1 if auto_advance else 0
    
    return [
        gr.update(value=new_step),  # current_step
        gr.update(value=completed_steps),  # completed_steps
        gr.update(value=step_data),  # step_data
        gr.update(value=create_step_indicator(new_step, completed_steps)),  # step_indicator
        gr.update(value="✅ 主题已确认"),  # status
        gr.update(),  # thinking_result
        gr.update(),  # plan_result
        gr.update(selected=1 if auto_advance else 0)  # tabs
    ]


async def execute_thinking_step(current_step: int, completed_steps: list, step_data: dict, auto_advance: bool):
    """执行思考步骤"""
    topic = step_data.get("topic", "")
    if not topic:
        return [gr.update()] * 8
    
    try:
        # 调用AI思考
        thinking_result = await ai_client.thinking_mode(topic)
        step_data["thinking_result"] = thinking_result
        completed_steps[1] = True
        
        # 如果启用自动跃迁，切换到下一步
        new_step = 2 if auto_advance else 1
        
        return [
            gr.update(value=new_step),  # current_step
            gr.update(value=completed_steps),  # completed_steps
            gr.update(value=step_data),  # step_data
            gr.update(value=create_step_indicator(new_step, completed_steps)),  # step_indicator
            gr.update(value="✅ 思考完成"),  # status
            gr.update(value=thinking_result),  # thinking_result
            gr.update(),  # plan_result
            gr.update(selected=2 if auto_advance else 1)  # tabs
        ]
    except Exception as e:
        logger.error(f"思考步骤失败: {str(e)}")
        return [
            gr.update(),  # current_step
            gr.update(),  # completed_steps
            gr.update(),  # step_data
            gr.update(),  # step_indicator
            gr.update(value=f"❌ 思考失败: {str(e)}"),  # status
            gr.update(),  # thinking_result
            gr.update(),  # plan_result
            gr.update()   # tabs
        ]


async def execute_planning_step(current_step: int, completed_steps: list, step_data: dict, auto_advance: bool):
    """执行计划步骤"""
    topic = step_data.get("topic", "")
    thinking_result = step_data.get("thinking_result", "")
    
    if not topic or not thinking_result:
        return [gr.update()] * 8
    
    try:
        # 调用AI制定计划
        plan_result = await ai_client.planning_mode(topic, thinking_result)
        step_data["plan_result"] = plan_result
        completed_steps[2] = True
        
        # 如果启用自动跃迁，切换到下一步
        new_step = 3 if auto_advance else 2
        
        return [
            gr.update(value=new_step),  # current_step
            gr.update(value=completed_steps),  # completed_steps
            gr.update(value=step_data),  # step_data
            gr.update(value=create_step_indicator(new_step, completed_steps)),  # step_indicator
            gr.update(value="✅ 研究计划已生成"),  # status
            gr.update(),  # thinking_result
            gr.update(value=plan_result),  # plan_result
            gr.update(selected=3 if auto_advance else 2)  # tabs
        ]
    except Exception as e:
        logger.error(f"计划步骤失败: {str(e)}")
        return [
            gr.update(),  # current_step
            gr.update(),  # completed_steps
            gr.update(),  # step_data
            gr.update(),  # step_indicator
            gr.update(value=f"❌ 计划生成失败: {str(e)}"),  # status
            gr.update(),  # thinking_result
            gr.update(),  # plan_result
            gr.update()   # tabs
        ]


async def execute_current_step(current_step: int, completed_steps: list, step_data: dict, 
                             topic_input: str, auto_advance: bool):
    """执行当前步骤的统一入口"""
    if current_step == 0:
        return await execute_topic_step(topic_input, current_step, completed_steps, step_data, auto_advance)
    elif current_step == 1:
        return await execute_thinking_step(current_step, completed_steps, step_data, auto_advance)
    elif current_step == 2:
        return await execute_planning_step(current_step, completed_steps, step_data, auto_advance)
    else:
        return [gr.update()] * 8


def create_app():
    """创建简化版应用"""
    with gr.Blocks(theme=gr.themes.Soft(), title="深度研究工具") as app:
        
        # 状态管理
        current_step = gr.State(value=0)
        completed_steps = gr.State(value=[False] * 6)
        step_data = gr.State(value={})
        
        # 标题
        gr.Markdown("# 🔬 AI 深度研究工具")
        
        # 步骤指示器
        step_indicator = gr.HTML(value=create_step_indicator(0, [False] * 6))
        
        # 主要内容区域
        with gr.Tabs() as tabs:
            with gr.Tab("1. 主题输入"):
                gr.Markdown("## 📝 确定研究主题")
                topic_input = gr.Textbox(
                    label="请输入您要研究的主题",
                    placeholder="例如：人工智能在医疗领域的应用现状与发展趋势",
                    lines=3
                )
                gr.Examples(
                    examples=[
                        "区块链技术在供应链管理中的应用",
                        "可持续能源发展的挑战与机遇", 
                        "远程工作对企业文化的影响"
                    ],
                    inputs=topic_input
                )
            
            with gr.Tab("2. AI思考"):
                gr.Markdown("## 🤔 AI深度思考")
                thinking_result = gr.Markdown(value="")
            
            with gr.Tab("3. 研究计划"):
                gr.Markdown("## 📋 制定研究计划")
                plan_result = gr.Markdown(value="")
            
            with gr.Tab("4. 信息收集"):
                gr.Markdown("## 🔍 信息收集")
                gr.Markdown("*此功能将在后续版本实现*")
            
            with gr.Tab("5. 深度研究"):
                gr.Markdown("## 🔬 深度研究")
                gr.Markdown("*此功能将在后续版本实现*")
            
            with gr.Tab("6. 生成报告"):
                gr.Markdown("## 📊 生成最终报告")
                gr.Markdown("*此功能将在后续版本实现*")
        
        # 控制按钮
        with gr.Row():
            execute_btn = gr.Button("执行当前步骤", variant="primary")
            auto_advance = gr.Checkbox(label="自动进入下一步", value=True)
        
        # 状态显示
        status_display = gr.HTML(value="")
        
        # 事件绑定
        execute_btn.click(
            fn=execute_current_step,
            inputs=[current_step, completed_steps, step_data, topic_input, auto_advance],
            outputs=[current_step, completed_steps, step_data, step_indicator, status_display, thinking_result, plan_result, tabs]
        )
    
    return app


if __name__ == "__main__":
    try:
        settings.validate_environment()
        logger.info("启动简化版深度研究工具...")
        
        app = create_app()
        app.launch(server_name="0.0.0.0", server_port=7861, share=False)
        
    except Exception as e:
        print(f"应用启动失败: {str(e)}")
        sys.exit(1)
