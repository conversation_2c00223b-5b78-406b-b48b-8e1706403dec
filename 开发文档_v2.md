# 基于 Gradio 的深度研究工具开发文档 v2

## 变更摘要（v2）

- 新增“逐步引导式”交互规范与状态机设计（2.3）
- **新增自动跃迁机制设计（2.4）**
  - 步骤完成后自动跳转到下一步
  - 倒计时显示与取消功能
  - 智能暂停与异常处理
- 新增步骤数据契约与缓存/失效策略（4.5）
- **新增完整的 UI/UX 设计规范（第 6 章）**
  - 单页面步骤式交互设计与布局结构（6.1）
  - 思考过程半隐式显示设计与组件实现（6.2）
  - 步骤间动态控制与状态管理（6.3）
  - 实时交互与用户体验优化（6.7）
  - 步骤状态管理器与事件绑定架构（6.8）
  - 增强的错误处理与用户反馈（6.9）
  - 完整 CSS 样式定义与动画效果（6.10）
- 新增 Gradio 事件线路图与控件启用/禁用规则（6.4-6.5）
- 新增端到端与回归测试建议（8.3）
- 环境变量示例替换为占位值并补充密钥安全说明（3.3）

---

## 项目概述

基于原 Deep Research 项目，使用 Gradio 框架开发一个 AI 驱动的深度研究工具，支持快速生成综合性研究报告。

## 1. 项目架构设计

### 1.1 整体架构

```
gradio-deep-research/
├── app.py                 # Gradio主应用
├── config/
│   ├── __init__.py
│   ├── settings.py        # 配置管理
│   └── models.py          # 数据模型
├── core/
│   ├── __init__.py
│   ├── ai_client.py       # AI模型客户端
│   ├── search_engine.py   # 搜索引擎集成
│   ├── content_retriever.py # 内容检索器
│   ├── research_engine.py # 研究引擎核心
│   ├── thinking_engine.py # 思考引擎
│   ├── planning_engine.py # 计划引擎
│   └── report_generator.py # 报告生成器
├── storage/
│   ├── __init__.py
│   ├── local_storage.py   # 本地存储管理
│   ├── research_history.py # 研究历史管理
│   └── knowledge_base.py  # 本地知识库
├── utils/
│   ├── __init__.py
│   ├── markdown_utils.py  # Markdown处理
│   ├── file_utils.py      # 文件处理
│   ├── serp_generator.py  # SERP查询生成器
│   └── logger.py          # 日志工具
├── ui/
│   ├── __init__.py
│   ├── components.py      # UI组件
│   ├── interface.py       # 界面定义
│   └── workflow_ui.py     # 工作流程UI
├── requirements.txt       # 依赖包
├── .env.example          # 环境变量示例
└── README.md             # 项目说明
```

### 1.2 核心功能模块

#### AI 模型集成模块

- 功能：集成 Qwen3-8B 模型，支持思考和任务两种模式
- 配置：使用 SiliconFlow API 代理
- 实现：基于 OpenAI 兼容接口

#### 搜索引擎模块

- 功能：使用 Tavily 进行网络搜索
- 特性：支持多语言搜索，可配置搜索结果数量
- API 集成：Tavily Search API

#### 内容检索模块

- 功能：使用 Jina Reader API 获取网页内容
- 特性：自动提取和清理网页内容
- 格式：返回 Markdown 格式的内容

## 2. 完整的研究流程架构

### 2.1 六个核心阶段

```python
class ResearchPhase(Enum):
    TOPIC_INPUT = "topic_input"           # 确定研究主题
    THINKING = "thinking"                 # 启动思考过程
    PLANNING = "planning"                 # 提出研究思路
    COLLECTION = "collection"             # 信息收集
    DEEP_RESEARCH = "deep_research"       # 深度研究（可重复）
    FINAL_REPORT = "final_report"         # 生成最终报告
```

### 2.2 完整工作流程

```mermaid
flowchart TB
    A[确定研究主题] --> B[启动思考过程]
    B --> C[AI提出问题]
    C --> D{用户是否回答?}
    D -->|是| E[处理用户回答]
    D -->|否| F[编写研究计划]
    E --> F
    F --> G[生成SERP查询词]
    G --> H[初始信息收集]
    H --> I[本地资源检索]
    H --> J[网络信息收集]
    I --> K[深度研究判断]
    J --> K
    K --> L{需要更深入研究?}
    L -->|是| M[生成研究建议]
    M --> N[新一轮信息收集]
    N --> K
    L -->|否| O[生成最终报告]
    O --> P[用户可重新生成]
```

### 2.3 一步步引导式交互规范（新增）

- 状态机与跃迁
  - 仅允许 current_step 完成时进入 next_step；可回退到任一步骤
  - 重做某步会“级联失效”该步与后续步骤的结果与 UI 输出
- 按钮与可用性
  - 执行本步：始终可见；仅当前步可点击
  - 下一步：只有当本步校验通过并产出有效结果时启用
  - 上一步：始终启用；回退后禁用下游“下一步”
  - 重做本步：仅当本步已完成时启用；点击后清理本步及后续缓存
- 可视化与导航
  - 使用 Stepper/Tabs：仅允许访问“已完成”和“当前”步骤；未完成步骤禁用
  - 显示状态：等待输入/执行中/已完成/失败
- 失败与恢复
  - 失败时禁用“下一步”，保留“重试本步/上一步”
  - 可配置自动重试（如 API 调用重试 3 次，指数退避）

### 2.4 自动跃迁机制设计（新增）

- **自动跃迁流程**
  - 步骤执行完成 → 显示结果（1-2 秒） → 自动跳转到下一步
  - 跳转前显示倒计时："3 秒后自动进入下一步..."
  - 用户可在倒计时期间点击"取消自动跳转"
- **控制选项**
  - 全局开关：启用/禁用自动跃迁
  - 步骤级控制：某些步骤可设置为手动确认（如最终报告生成）
  - 暂停模式：用户可随时暂停自动跃迁，改为手动控制
- **用户体验优化**
  - 平滑过渡动画：步骤切换时的视觉效果
  - 进度保持：自动跃迁不影响用户回退到之前步骤
  - 智能暂停：检测到用户交互时自动暂停跃迁
- **异常处理**
  - 执行失败时停止自动跃迁
  - 网络异常时提供手动重试选项
  - 长时间执行的步骤显示进度而非立即跳转

## 3. 技术规格

### 3.1 开发环境

- Python 版本：3.9+
- 主要框架：Gradio 4.x
- AI 库：openai (兼容接口)
- HTTP 库：requests, httpx
- 其他：pydantic, python-dotenv

### 3.2 依赖包清单

```txt
gradio>=4.0.0
openai>=1.0.0
requests>=2.31.0
httpx>=0.25.0
pydantic>=2.0.0
python-dotenv>=1.0.0
markdown>=3.5.0
beautifulsoup4>=4.12.0
aiohttp>=3.9.0
tavily-python>=0.3.0
```

> 安全提示：不要在仓库或文档中保存真实密钥；请使用 .env（本地）或部署平台环境变量注入。

### 3.3 环境变量配置（使用占位值）

```env
# AI模型配置
API_KEY=YOUR_SILICONFLOW_API_KEY
API_PROXY=https://api.siliconflow.cn/v1
AI_MODEL=Qwen/Qwen3-8B

# 搜索工具配置
TAVILY_API_KEY=YOUR_TAVILY_API_KEY

# Jina Reader配置
JINA_READER_BASE_URL=https://r.jina.ai

# 应用配置
MAX_SEARCH_RESULTS=10
RESEARCH_TIMEOUT=300
LOG_LEVEL=INFO
```

## 4. 核心类设计

### 4.1 思考引擎

```python
class ThinkingEngine:
    async def analyze_topic(self, topic: str) -> ThinkingResult
    async def generate_questions(self, topic: str) -> List[str]
    async def process_user_answers(self, questions: List[str], answers: List[str]) -> str
    async def rethink(self, topic: str, previous_context: str) -> ThinkingResult
```

### 4.2 计划引擎

```python
class PlanningEngine:
    async def create_research_plan(self, topic: str, thinking_result: str) -> ResearchPlan
    async def rewrite_plan(self, original_plan: str, feedback: str) -> ResearchPlan
    async def generate_serp_queries(self, plan: ResearchPlan) -> List[str]
```

### 4.3 本地知识库

```python
class LocalKnowledgeBase:
    def upload_documents(self, files: List[str]) -> bool
    def search_local_resources(self, query: str) -> List[str]
    def index_content(self, content: str, metadata: dict) -> bool
```

### 4.4 深度研究循环

```python
class DeepResearchLoop:
    async def suggest_further_research(self, current_materials: List[str]) -> List[str]
    async def conduct_additional_research(self, suggestions: List[str]) -> List[str]
    def should_continue_research(self, materials: List[str]) -> bool
```

### 4.5 步骤数据契约与缓存（新增）

- 键名规范：{session_id}:{phase}:{field}
- I/O 契约（简版）
  - TOPIC_INPUT: in=用户输入; out=topic_cleaned:str
  - THINKING: in=topic_cleaned; out=thinking_result:dict
  - PLANNING: in=thinking_result; out=research_plan:dict, serp_queries:list[str]
  - COLLECTION: in=serp_queries, local_kb_refs; out=materials:list[DocRef]
  - DEEP_RESEARCH: in=materials; out=refined_materials:list[DocRef], suggestions:list[str]
  - FINAL_REPORT: in=refined_materials|materials; out=final_report_md:str
- 失效策略：回退/重做某步后，清理该步及其后的缓存与 UI 输出；仅保留上游有效数据
- 持久化：storage/research_history.py 可按 session 保存中间产物与错误日志

## 5. 开发计划

### 阶段一：基础框架 (1-2 天)

1. 项目结构搭建
2. 环境配置和依赖安装
3. 基础 Gradio 界面创建
4. AI 客户端基础实现

### 阶段二：核心功能 (2-3 天)

1. 思考引擎实现
2. 计划引擎实现
3. Tavily 搜索引擎集成
4. Jina Reader 内容检索实现

### 阶段三：工作流程 (2-3 天)

1. 完整研究流程实现
2. 状态管理和持久化
3. 深度研究循环机制
4. 本地知识库支持

### 阶段四：界面优化 (1-2 天)

1. 多阶段界面完善
2. 实时进度显示
3. 用户体验优化
4. 错误处理完善

### 阶段五：高级功能 (1-2 天)

1. 研究历史管理
2. 报告导出功能
3. 配置管理界面
4. 性能优化

## 6. UI/UX 设计规范（新增）

### 6.1 单页面步骤式交互设计

#### 6.1.1 整体布局结构

```python
# Gradio 界面布局示例
with gr.Blocks(theme=gr.themes.Soft()) as app:
    # 全局状态
    current_step = gr.State(value="topic_input")
    step_data = gr.State(value={})
    completed_steps = gr.State(value=set())

    # 顶部进度指示器
    with gr.Row():
        step_indicator = gr.HTML(value=generate_step_indicator(0))

    # 主要内容区域 - 每个步骤的容器
    with gr.Column():
        # 步骤1: 主题输入
        with gr.Group(visible=True) as step1_container:
            step1_content = create_topic_input_step()

        # 步骤2: 思考过程（初始隐藏）
        with gr.Group(visible=False) as step2_container:
            step2_content = create_thinking_step()

        # ... 其他步骤容器

    # 底部控制按钮区域
    with gr.Row():
        btn_prev = gr.Button("上一步", interactive=False)
        btn_execute = gr.Button("执行当前步骤", variant="primary")
        btn_next = gr.Button("下一步", interactive=False, visible=False)  # 自动跃迁时隐藏
        btn_redo = gr.Button("重做本步", interactive=False)

    # 自动跃迁控制区域
    with gr.Row():
        auto_advance_toggle = gr.Checkbox(
            label="启用自动跃迁",
            value=True,
            info="步骤完成后自动进入下一步"
        )
        auto_advance_countdown = gr.HTML(
            value="",
            visible=False,
            elem_classes=["countdown-display"]
        )
        cancel_auto_advance = gr.Button(
            "取消自动跳转",
            variant="secondary",
            size="sm",
            visible=False
        )
```

#### 6.1.2 步骤控件显示逻辑

- **渐进式显示**：只有当前步骤和已完成步骤的容器可见
- **动态启用**：按钮状态根据步骤完成情况实时更新
- **视觉反馈**：完成的步骤显示绿色勾选，当前步骤高亮显示

#### 6.1.3 各步骤具体 UI 组件

```python
def create_topic_input_step():
    """步骤1：主题输入"""
    with gr.Column():
        gr.Markdown("## 📝 第一步：确定研究主题")
        topic_input = gr.Textbox(
            label="请输入您要研究的主题",
            placeholder="例如：人工智能在医疗领域的应用现状与发展趋势",
            lines=3
        )
        topic_examples = gr.Examples(
            examples=[
                "区块链技术在供应链管理中的应用",
                "可持续能源发展的挑战与机遇",
                "远程工作对企业文化的影响"
            ],
            inputs=topic_input
        )
    return topic_input

def create_thinking_step():
    """步骤2：AI思考过程"""
    with gr.Column():
        gr.Markdown("## 🤔 第二步：AI深度思考")

        # 思考过程展示区域
        thinking_container, thinking_toggle, thinking_content = create_thinking_display()

        # AI提出的问题（如果有）
        ai_questions = gr.Markdown(visible=False)
        user_answers = gr.Textbox(
            label="您的回答（可选）",
            placeholder="请回答AI提出的问题，或直接跳过进入下一步",
            lines=5,
            visible=False
        )
    return thinking_container, ai_questions, user_answers

def create_planning_step():
    """步骤3：研究计划"""
    with gr.Column():
        gr.Markdown("## 📋 第三步：制定研究计划")
        research_plan = gr.Markdown()
        plan_feedback = gr.Textbox(
            label="对研究计划的反馈（可选）",
            placeholder="如果您对研究计划有建议或修改意见，请在此输入",
            lines=3
        )
        serp_queries = gr.JSON(label="生成的搜索关键词", visible=False)
    return research_plan, plan_feedback, serp_queries

def create_collection_step():
    """步骤4：信息收集"""
    with gr.Column():
        gr.Markdown("## 🔍 第四步：信息收集")

        # 本地知识库上传
        with gr.Accordion("本地知识库", open=False):
            file_upload = gr.File(
                label="上传相关文档",
                file_count="multiple",
                file_types=[".pdf", ".txt", ".md", ".docx"]
            )
            local_search = gr.Textbox(
                label="搜索本地资源",
                placeholder="输入关键词搜索已上传的文档"
            )

        # 收集进度显示
        collection_progress = gr.HTML()
        collected_materials = gr.JSON(label="收集到的资料", visible=False)
    return file_upload, local_search, collection_progress, collected_materials

def create_deep_research_step():
    """步骤5：深度研究"""
    with gr.Column():
        gr.Markdown("## 🔬 第五步：深度研究")
        research_suggestions = gr.Markdown()
        continue_research = gr.Checkbox(
            label="继续深入研究",
            value=True
        )
        research_progress = gr.HTML()
    return research_suggestions, continue_research, research_progress

def create_final_report_step():
    """步骤6：生成报告"""
    with gr.Column():
        gr.Markdown("## 📊 第六步：生成最终报告")
        report_content = gr.Markdown()

        # 报告操作按钮
        with gr.Row():
            regenerate_btn = gr.Button("重新生成报告", variant="secondary")
            export_btn = gr.Button("导出报告", variant="primary")

        # 导出选项
        export_format = gr.Radio(
            choices=["Markdown", "PDF", "Word"],
            value="Markdown",
            label="导出格式"
        )
    return report_content, regenerate_btn, export_btn, export_format
```

### 6.2 思考过程半隐式显示设计

#### 6.2.1 思考链展示组件

```python
def create_thinking_display():
    """创建类似大模型思考链的展示组件"""
    with gr.Column() as thinking_container:
        # 思考过程标题栏（可折叠）
        with gr.Row():
            thinking_toggle = gr.Button(
                "🤔 AI思考过程 (点击展开/收起)",
                variant="secondary",
                size="sm"
            )
            thinking_status = gr.HTML("思考中...")

        # 思考内容区域（默认折叠）
        with gr.Column(visible=False) as thinking_content:
            # 实时思考流
            thinking_stream = gr.Markdown(
                value="",
                elem_classes=["thinking-stream"]
            )

            # 思考结论
            thinking_conclusion = gr.Markdown(
                value="",
                elem_classes=["thinking-conclusion"]
            )

    return thinking_container, thinking_toggle, thinking_content
```

#### 6.2.2 思考过程样式定义

```css
/* 自定义CSS样式 */
.thinking-stream {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-left: 4px solid #4a90e2;
  padding: 15px;
  margin: 10px 0;
  border-radius: 8px;
  font-family: "Monaco", "Menlo", monospace;
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.8;
}

.thinking-conclusion {
  background: #e8f5e8;
  border: 1px solid #4caf50;
  padding: 15px;
  border-radius: 8px;
  margin-top: 10px;
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.step-item {
  flex: 1;
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  margin: 0 5px;
}

.step-completed {
  background: #4caf50;
  color: white;
}

.step-current {
  background: #2196f3;
  color: white;
}

.step-pending {
  background: #f5f5f5;
  color: #999;
}
```

### 6.3 步骤间动态控制实现

#### 6.3.1 状态管理函数

```python
def update_step_visibility(current_step: str, completed_steps: set):
    """更新步骤容器的可见性"""
    visibility_map = {
        "topic_input": current_step == "topic_input" or "topic_input" in completed_steps,
        "thinking": current_step == "thinking" or "thinking" in completed_steps,
        "planning": current_step == "planning" or "planning" in completed_steps,
        # ... 其他步骤
    }
    return [gr.update(visible=v) for v in visibility_map.values()]

def update_button_states(current_step: str, completed_steps: set, step_data: dict):
    """更新控制按钮的状态"""
    step_order = ["topic_input", "thinking", "planning", "collection", "deep_research", "final_report"]
    current_index = step_order.index(current_step)

    # 上一步按钮
    prev_enabled = current_index > 0

    # 下一步按钮
    next_enabled = current_step in completed_steps and current_index < len(step_order) - 1

    # 重做按钮
    redo_enabled = current_step in completed_steps

    # 执行按钮
    execute_enabled = True  # 始终可执行当前步骤

    return [
        gr.update(interactive=prev_enabled),
        gr.update(interactive=execute_enabled),
        gr.update(interactive=next_enabled),
        gr.update(interactive=redo_enabled)
    ]
```

#### 6.3.2 步骤执行与状态更新

```python
async def execute_step(step_name: str, step_data: dict, *args):
    """执行指定步骤并更新状态"""
    try:
        # 显示执行状态
        yield gr.update(value="执行中..."), gr.update(interactive=False)

        # 根据步骤类型执行相应逻辑
        if step_name == "thinking":
            result = await execute_thinking_step(*args)
            # 实时更新思考过程
            for thinking_chunk in result.thinking_stream:
                yield gr.update(value=thinking_chunk)

        # 更新步骤数据和完成状态
        step_data[step_name] = result
        completed_steps.add(step_name)

        # 返回最终结果和更新的UI状态
        return result, update_ui_after_completion(step_name)

    except Exception as e:
        # 错误处理
        return handle_step_error(step_name, e)
```

## 7. 关键技术实现

### 7.1 异步处理

- 使用 asyncio 处理并发操作
- Gradio 的进度条实时更新
- 思考过程的流式显示

### 7.2 状态管理

- 基于 Gradio State 的会话管理
- 本地文件系统持久化
- 步骤间数据传递与缓存

### 7.3 错误处理（基础）

- API 调用失败重试机制
- 网络超时处理
- 用户友好的错误提示

### 6.4 Gradio 事件线路图（新增）

- 全局 State：
  - current_step: 当前步骤
  - completed_steps: 已完成步骤集合
  - step_data: 各步骤产物字典
- 事件绑定：
  - btn*exec*{step}.click -> run\_{step}（校验->执行->产物缓存->标记完成）
  - btn_next.click -> 仅当本步已完成，推进 current_step；未完成则拒绝
  - btn_prev.click -> 回退一步，级联失效后续缓存/完成状态
  - btn_redo.click -> 重做当前步，清理本步及后续缓存/完成状态
- 启用/禁用规则：
  - Tabs/Stepper：未完成的后续步骤禁用
  - “下一步”：依赖 ok_flag 或产物存在性校验
- UI 刷新：
  - 本步成功后刷新对应输出；回退/重做时清空后续输出

### 6.5 异常处理与重试（新增）

- 分类：输入校验失败、API 失败、超时、空结果、速率限制
- 策略：
  - 重试：指数退避、最大重试次数可配置
  - 降级：切换备用 API/减少结果数量
  - 反馈：toast/状态条提示；失败禁用“下一步”，保留“上一步/重试本步”
- 记录：写入 storage/local_storage.py（含上下文与栈）

### 6.7 实时交互与用户体验优化

#### 6.7.1 思考过程流式显示

```python
async def stream_thinking_process(topic: str, thinking_display):
    """流式显示AI思考过程"""
    thinking_prompt = f"请深入思考研究主题：{topic}"

    accumulated_thinking = ""
    async for chunk in ai_client.stream_completion(thinking_prompt):
        accumulated_thinking += chunk
        # 实时更新思考显示
        yield gr.update(value=accumulated_thinking)

    # 思考完成后显示结论
    conclusion = extract_thinking_conclusion(accumulated_thinking)
    yield gr.update(value=conclusion, elem_classes=["thinking-conclusion"])
```

#### 6.7.2 进度指示器动态更新

```python
def generate_step_indicator(current_step_index: int, completed_steps: set):
    """生成动态步骤指示器HTML"""
    steps = [
        ("主题输入", "topic_input"),
        ("AI思考", "thinking"),
        ("制定计划", "planning"),
        ("信息收集", "collection"),
        ("深度研究", "deep_research"),
        ("生成报告", "final_report")
    ]

    html_parts = []
    for i, (name, step_id) in enumerate(steps):
        if step_id in completed_steps:
            css_class = "step-completed"
            icon = "✓"
        elif i == current_step_index:
            css_class = "step-current"
            icon = "▶"
        else:
            css_class = "step-pending"
            icon = str(i + 1)

        html_parts.append(f'''
            <div class="step-item {css_class}">
                <div class="step-icon">{icon}</div>
                <div class="step-name">{name}</div>
            </div>
        ''')

    return f'<div class="step-indicator">{"".join(html_parts)}</div>'
```

### 6.8 步骤状态管理与控制逻辑

#### 6.8.1 步骤状态管理器

```python
class StepManager:
    """步骤状态管理器"""

    def __init__(self):
        self.step_order = ["topic_input", "thinking", "planning", "collection", "deep_research", "final_report"]
        self.current_step = "topic_input"
        self.completed_steps = set()
        self.step_data = {}
        self.auto_advance_enabled = True
        self.auto_advance_delay = 3  # 秒
        self.manual_steps = {"final_report"}  # 需要手动确认的步骤

    def can_advance_to_next(self) -> bool:
        """检查是否可以进入下一步"""
        return self.current_step in self.completed_steps

    def advance_step(self) -> bool:
        """前进到下一步"""
        if not self.can_advance_to_next():
            return False

        current_index = self.step_order.index(self.current_step)
        if current_index < len(self.step_order) - 1:
            self.current_step = self.step_order[current_index + 1]
            return True
        return False

    def go_back(self) -> bool:
        """回退到上一步"""
        current_index = self.step_order.index(self.current_step)
        if current_index > 0:
            self.current_step = self.step_order[current_index - 1]
            # 清理后续步骤的完成状态
            self.invalidate_subsequent_steps()
            return True
        return False

    def invalidate_subsequent_steps(self):
        """使后续步骤失效"""
        current_index = self.step_order.index(self.current_step)
        for step in self.step_order[current_index + 1:]:
            self.completed_steps.discard(step)
            self.step_data.pop(step, None)

    def should_auto_advance(self) -> bool:
        """检查是否应该自动跃迁"""
        return (
            self.auto_advance_enabled and
            self.current_step in self.completed_steps and
            self.current_step not in self.manual_steps
        )

    async def auto_advance_with_countdown(self, update_callback):
        """带倒计时的自动跃迁"""
        if not self.should_auto_advance():
            return False

        # 显示倒计时
        for i in range(self.auto_advance_delay, 0, -1):
            countdown_html = f"""
            <div class="auto-advance-countdown">
                <span class="countdown-icon">⏰</span>
                <span class="countdown-text">{i}秒后自动进入下一步...</span>
            </div>
            """
            await update_callback(countdown_html, True)  # 显示倒计时和取消按钮
            await asyncio.sleep(1)

        # 执行自动跃迁
        if self.should_auto_advance():  # 再次检查，防止用户取消
            success = self.advance_step()
            await update_callback("", False)  # 隐藏倒计时
            return success

        return False
```

#### 6.8.2 事件绑定架构

```python
def setup_event_handlers(app_components):
    """设置所有事件处理器"""
    # 执行当前步骤
    app_components['btn_execute'].click(
        fn=execute_current_step,
        inputs=[
            app_components['current_step'],
            app_components['step_data'],
            # ... 其他输入
        ],
        outputs=[
            app_components['step_indicator'],
            app_components['thinking_display'],
            # ... 其他输出
        ]
    )

    # 下一步按钮
    app_components['btn_next'].click(
        fn=advance_to_next_step,
        inputs=[app_components['current_step'], app_components['completed_steps']],
        outputs=get_all_step_containers() + get_all_buttons()
    )

    # 上一步按钮
    app_components['btn_prev'].click(
        fn=go_to_previous_step,
        inputs=[app_components['current_step']],
        outputs=get_all_step_containers() + get_all_buttons()
    )

    # 重做按钮
    app_components['btn_redo'].click(
        fn=redo_current_step,
        inputs=[app_components['current_step'], app_components['step_data']],
        outputs=get_all_step_containers() + [app_components['step_data']]
    )
```

### 6.9 增强的错误处理与用户反馈

#### 6.9.1 分层异常处理

```python
class StepExecutionError(Exception):
    """步骤执行异常"""
    def __init__(self, step_name: str, error_type: str, message: str, retry_count: int = 0):
        self.step_name = step_name
        self.error_type = error_type
        self.message = message
        self.retry_count = retry_count
        super().__init__(f"Step {step_name} failed: {message}")

async def execute_with_retry(step_func, max_retries: int = 3, backoff_factor: float = 2.0):
    """带重试的步骤执行"""
    for attempt in range(max_retries + 1):
        try:
            return await step_func()
        except Exception as e:
            if attempt == max_retries:
                raise StepExecutionError(
                    step_name=step_func.__name__,
                    error_type=type(e).__name__,
                    message=str(e),
                    retry_count=attempt
                )

            # 指数退避
            wait_time = backoff_factor ** attempt
            await asyncio.sleep(wait_time)
```

#### 6.9.2 用户友好的错误反馈

```python
def create_error_display(error: StepExecutionError):
    """创建错误显示组件"""
    error_html = f"""
    <div class="error-container">
        <div class="error-header">
            <span class="error-icon">⚠️</span>
            <span class="error-title">步骤执行失败</span>
        </div>
        <div class="error-details">
            <p><strong>步骤:</strong> {error.step_name}</p>
            <p><strong>错误类型:</strong> {error.error_type}</p>
            <p><strong>错误信息:</strong> {error.message}</p>
            <p><strong>重试次数:</strong> {error.retry_count}</p>
        </div>
        <div class="error-actions">
            <button class="retry-btn">重试</button>
            <button class="skip-btn">跳过此步骤</button>
        </div>
    </div>
    """
    return gr.HTML(value=error_html, elem_classes=["error-display"])
```

### 6.10 完整 CSS 样式定义

#### 6.10.1 扩展样式表

```css
/* 错误显示样式 */
.error-container {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 16px;
  margin: 10px 0;
}

.error-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.error-icon {
  font-size: 20px;
  margin-right: 8px;
}

.error-title {
  font-weight: bold;
  color: #c53030;
}

.error-details {
  background: #fefefe;
  padding: 12px;
  border-radius: 4px;
  margin: 8px 0;
  font-family: monospace;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 12px;
}

.retry-btn,
.skip-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.retry-btn {
  background: #3182ce;
  color: white;
}

.skip-btn {
  background: #718096;
  color: white;
}

/* 步骤指示器增强样式 */
.step-icon {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.step-name {
  font-size: 12px;
  font-weight: 500;
}

/* 思考过程动画效果 */
.thinking-stream {
  position: relative;
  overflow: hidden;
}

.thinking-stream::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: thinking-shimmer 2s infinite;
}

@keyframes thinking-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 步骤容器过渡效果 */
.step-container {
  transition: all 0.3s ease-in-out;
  opacity: 1;
}

.step-container.hidden {
  opacity: 0;
  height: 0;
  overflow: hidden;
}

/* 按钮状态样式 */
.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 自动跃迁倒计时样式 */
.auto-advance-countdown {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  margin: 10px 0;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  animation: pulse-glow 2s infinite;
}

.countdown-icon {
  font-size: 18px;
  margin-right: 8px;
  animation: tick 1s infinite;
}

.countdown-text {
  font-size: 14px;
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
  }
}

@keyframes tick {
  0%,
  50% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
}

/* 步骤切换过渡动画 */
.step-transition-enter {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.3s ease-out;
}

.step-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.step-transition-exit {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-in;
}

.step-transition-exit-active {
  opacity: 0;
  transform: translateX(-30px);
}
```

## 7. 部署方案

### 7.1 本地部署

```bash
python app.py
```

### 7.2 云端部署

- Hugging Face Spaces：免费 GPU 支持
- Railway：简单云部署
- Docker：容器化部署

### 7.3 端到端测试与回归（新增）

- 正向用例：依序完成 6 步，生成报告
- 回退/重做：在 PLANNING 或 DEEP_RESEARCH 回退并重做，验证下游失效与重新产出
- 失败注入：模拟 API 超时/空结果/429，验证 UI 提示与禁用逻辑
- 持久化：刷新页面后恢复会话与 current_step/缓存一致性

## 8. 预期效果

### 8.1 核心功能

- 完整的六阶段研究流程
- 逐步引导、可回退/重做的稳健交互
- 高质量的研究报告生成
- 灵活的深度研究循环

### 8.2 用户体验

- **单页面渐进式交互**：每完成一步才显示下一步，避免用户迷失
- **思考过程可视化**：类似大模型思考链的半隐式显示，可折叠展开
- **实时状态反馈**：进度指示器、按钮状态、加载动画等视觉反馈
- **智能错误处理**：友好的错误提示、自动重试、降级策略

### 8.3 技术特性

- **响应式设计**：适配不同屏幕尺寸
- **流式显示**：思考过程实时流式更新
- **状态持久化**：支持页面刷新后恢复会话
- **性能优化**：异步处理、缓存机制、懒加载

### 8.4 开发优势

- **模块化架构**：清晰的代码结构，易于维护和扩展
- **完整的错误处理**：分层异常处理，提高系统稳定性
- **详细的样式规范**：统一的 UI 风格，提升用户体验
- **测试友好**：支持端到端测试和回归测试
