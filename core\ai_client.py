"""AI模型客户端"""

import asyncio
import logging
from typing import Async<PERSON>enerator, Dict, Any, Optional, List
from openai import AsyncOpenAI
from config.settings import settings
from config.models import APIError, RETRY_CONFIG

logger = logging.getLogger(__name__)


class AIClient:
    """AI模型客户端类"""

    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.API_KEY, base_url=settings.API_PROXY)
        self.model = settings.AI_MODEL

    async def _make_request_with_retry(self, request_func, *args, **kwargs):
        """带重试机制的请求"""
        max_retries = RETRY_CONFIG["max_retries"]
        backoff_factor = RETRY_CONFIG["backoff_factor"]

        for attempt in range(max_retries + 1):
            try:
                return await request_func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries:
                    raise APIError(
                        service="SiliconFlow",
                        error_code="MAX_RETRIES_EXCEEDED",
                        message=f"请求失败，已重试{max_retries}次: {str(e)}",
                    )

                # 指数退避
                wait_time = backoff_factor**attempt
                logger.warning(
                    f"API请求失败，{wait_time}秒后重试 (第{attempt + 1}次): {str(e)}"
                )
                await asyncio.sleep(wait_time)

    async def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """聊天完成接口"""
        try:
            response = await self._make_request_with_retry(
                self.client.chat.completions.create,
                model=self.model,
                messages=messages,
                **kwargs,
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"聊天完成请求失败: {str(e)}")
            raise APIError("SiliconFlow", "CHAT_COMPLETION_FAILED", str(e))

    async def stream_completion(
        self, messages: List[Dict[str, str]], **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天完成"""
        try:
            stream = await self._make_request_with_retry(
                self.client.chat.completions.create,
                model=self.model,
                messages=messages,
                stream=True,
                **kwargs,
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            logger.error(f"流式完成请求失败: {str(e)}")
            raise APIError("SiliconFlow", "STREAM_COMPLETION_FAILED", str(e))

    async def thinking_mode(self, topic: str) -> str:
        """思考模式 - 深度分析主题"""
        messages = [
            {
                "role": "system",
                "content": """你是一个专业的研究助手。请对用户提供的研究主题进行深度思考和分析。

请按以下结构回答：
1. 主题理解：简要概括主题的核心内容
2. 关键问题：列出3-5个需要深入研究的关键问题
3. 研究角度：从不同角度分析这个主题
4. 潜在挑战：指出研究过程中可能遇到的挑战
5. 预期价值：说明研究这个主题的价值和意义""",
            },
            {"role": "user", "content": f"请深入思考这个研究主题：{topic}"},
        ]

        return await self.chat_completion(messages)

    async def planning_mode(
        self, topic: str, thinking_result: str, user_feedback: Optional[str] = None
    ) -> str:
        """计划模式 - 制定研究计划"""
        user_context = f"\n\n用户反馈：{user_feedback}" if user_feedback else ""

        messages = [
            {
                "role": "system",
                "content": """你是一个专业的研究规划师。基于之前的思考结果，制定详细的研究计划。

请按以下结构制定计划：
1. 研究目标：明确具体的研究目标
2. 研究方法：说明采用的研究方法和策略
3. 关键领域：列出需要重点研究的领域
4. 信息来源：规划主要的信息获取渠道
5. 预期成果：描述预期的研究成果形式""",
            },
            {
                "role": "user",
                "content": f"研究主题：{topic}\n\n思考结果：{thinking_result}{user_context}\n\n请制定详细的研究计划。",
            },
        ]

        return await self.chat_completion(messages)


# 创建全局AI客户端实例
ai_client = AIClient()


def get_ai_client() -> AIClient:
    """获取AI客户端实例"""
    return ai_client
