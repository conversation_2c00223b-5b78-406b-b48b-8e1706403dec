# 深度研究工具

基于 Gradio 和 AI 的智能研究助手，能够帮助用户进行深度的主题研究并生成综合性报告。

## 功能特性

- 🤖 AI 驱动的研究流程
- 📝 六步式研究工作流
- 🔍 智能信息收集
- 📊 自动报告生成
- 💾 本地知识库支持

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并配置您的 API 密钥：

```bash
cp .env.example .env
```

### 3. 运行应用

```bash
python app.py
```

应用将在 http://localhost:7860 启动。

## 研究流程

1. **主题输入** - 确定研究主题
2. **AI思考** - AI深度分析主题
3. **制定计划** - 生成研究计划
4. **信息收集** - 收集相关资料
5. **深度研究** - 进行深入分析
6. **生成报告** - 输出最终报告

## 技术栈

- **前端框架**: Gradio 4.x
- **AI模型**: Qwen3-8B (通过 SiliconFlow)
- **搜索引擎**: Tavily API
- **内容获取**: Jina Reader API
- **数据验证**: Pydantic

## 项目结构

```
gradio-deep-research/
├── app.py                 # 主应用入口
├── config/                # 配置管理
├── core/                  # 核心功能
├── storage/               # 存储管理
├── utils/                 # 工具函数
├── ui/                    # 用户界面
├── requirements.txt       # 依赖包
├── .env.example          # 环境变量示例
└── README.md             # 项目说明
```

## 开发状态

当前版本实现了基础框架和前三个步骤：
- ✅ 主题输入
- ✅ AI思考
- ✅ 研究计划
- 🚧 信息收集（开发中）
- 🚧 深度研究（开发中）
- 🚧 报告生成（开发中）
