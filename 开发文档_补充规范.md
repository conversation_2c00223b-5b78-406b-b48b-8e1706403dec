# 开发文档补充规范

## 1. 数据模型定义

### 1.1 核心数据结构

```python
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from enum import Enum

class ResearchPhase(Enum):
    TOPIC_INPUT = "topic_input"
    THINKING = "thinking"
    PLANNING = "planning"
    COLLECTION = "collection"
    DEEP_RESEARCH = "deep_research"
    FINAL_REPORT = "final_report"

class ThinkingResult(BaseModel):
    topic: str
    analysis: str
    questions: List[str] = []
    insights: List[str] = []
    timestamp: str

class ResearchPlan(BaseModel):
    topic: str
    objectives: List[str]
    methodology: str
    key_areas: List[str]
    timeline: Optional[str] = None
    
class DocRef(BaseModel):
    title: str
    url: Optional[str] = None
    content: str
    source: str  # "web", "local", "api"
    relevance_score: float = 0.0
    timestamp: str

class SessionData(BaseModel):
    session_id: str
    current_phase: ResearchPhase
    completed_phases: List[ResearchPhase] = []
    step_data: Dict[str, Any] = {}
    created_at: str
    updated_at: str
```

## 2. API 集成规范

### 2.1 错误处理标准

```python
class APIError(Exception):
    def __init__(self, service: str, error_code: str, message: str):
        self.service = service
        self.error_code = error_code
        self.message = message
        super().__init__(f"{service} API Error [{error_code}]: {message}")

# 标准错误码
ERROR_CODES = {
    "RATE_LIMIT": "API调用频率超限",
    "AUTH_FAILED": "API密钥验证失败", 
    "TIMEOUT": "请求超时",
    "INVALID_RESPONSE": "响应格式错误",
    "SERVICE_UNAVAILABLE": "服务不可用"
}
```

### 2.2 重试策略配置

```python
RETRY_CONFIG = {
    "max_retries": 3,
    "backoff_factor": 2.0,
    "retry_on_errors": ["TIMEOUT", "SERVICE_UNAVAILABLE", "RATE_LIMIT"],
    "timeout_seconds": 30
}
```

## 3. 会话管理机制

### 3.1 会话生成和存储

```python
import uuid
from datetime import datetime, timedelta

class SessionManager:
    def __init__(self, storage_path: str = "storage/sessions"):
        self.storage_path = storage_path
        self.session_timeout = timedelta(hours=24)
    
    def create_session(self) -> str:
        session_id = str(uuid.uuid4())
        session_data = SessionData(
            session_id=session_id,
            current_phase=ResearchPhase.TOPIC_INPUT,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        self.save_session(session_data)
        return session_id
    
    def cleanup_expired_sessions(self):
        # 清理超时会话的实现
        pass
```

## 4. 文件处理规范

### 4.1 支持的文件类型和限制

```python
FILE_CONFIG = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "supported_formats": {
        ".pdf": "application/pdf",
        ".txt": "text/plain", 
        ".md": "text/markdown",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    },
    "max_files_per_session": 20
}
```

### 4.2 文件安全处理

```python
import hashlib
import os

def secure_filename(filename: str) -> str:
    """生成安全的文件名"""
    name, ext = os.path.splitext(filename)
    safe_name = "".join(c for c in name if c.isalnum() or c in ('-', '_'))
    return f"{safe_name[:50]}{ext}"

def calculate_file_hash(file_content: bytes) -> str:
    """计算文件哈希值用于去重"""
    return hashlib.sha256(file_content).hexdigest()
```

## 5. 简化的 UI 实现方案

### 5.1 使用 Gradio 原生组件

```python
def create_simplified_interface():
    with gr.Blocks(theme=gr.themes.Soft()) as app:
        # 使用 Tabs 而不是复杂的容器管理
        with gr.Tabs() as tabs:
            with gr.Tab("1. 主题输入", id="topic"):
                topic_input = gr.Textbox(label="研究主题")
                
            with gr.Tab("2. AI思考", id="thinking", interactive=False):
                with gr.Accordion("思考过程", open=False):
                    thinking_display = gr.Markdown()
                
            with gr.Tab("3. 研究计划", id="planning", interactive=False):
                plan_display = gr.Markdown()
                
            # ... 其他标签页
        
        # 简化的控制按钮
        with gr.Row():
            execute_btn = gr.Button("执行当前步骤", variant="primary")
            auto_continue = gr.Checkbox("自动继续到下一步", value=True)
            
        return app
```

### 5.2 状态管理简化

```python
# 使用更简单的状态管理
def create_state_management():
    session_state = gr.State(value={
        "current_step": 0,
        "completed_steps": [],
        "step_results": {}
    })
    
    def update_step(step_index: int, result: Any, state: dict):
        state["step_results"][step_index] = result
        if step_index not in state["completed_steps"]:
            state["completed_steps"].append(step_index)
        return state
    
    return session_state, update_step
```

## 6. 部署和配置建议

### 6.1 环境变量验证

```python
import os
from typing import Optional

def validate_environment() -> bool:
    """验证必需的环境变量"""
    required_vars = [
        "API_KEY",
        "TAVILY_API_KEY", 
        "API_PROXY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    return True
```

### 6.2 日志配置

```python
import logging
from datetime import datetime

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'logs/app_{datetime.now().strftime("%Y%m%d")}.log'),
            logging.StreamHandler()
        ]
    )
```
