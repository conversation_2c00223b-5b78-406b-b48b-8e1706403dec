"""主界面定义"""

import gradio as gr
import uuid
import asyncio
import logging
from typing import Dict, Any, List, Tuple
from config.models import ResearchPhase, SessionData
from config.settings import settings
from core.ai_client import get_ai_client
from ui.components import (
    create_step_indicator,
    create_topic_input_step,
    create_thinking_step,
    create_planning_step,
    create_navigation_controls,
    create_error_display,
    create_success_display
)

logger = logging.getLogger(__name__)


class ResearchInterface:
    """研究工具主界面类"""
    
    def __init__(self):
        self.ai_client = get_ai_client()
        self.step_order = [
            ResearchPhase.TOPIC_INPUT,
            ResearchPhase.THINKING,
            ResearchPhase.PLANNING,
            ResearchPhase.COLLECTION,
            ResearchPhase.DEEP_RESEARCH,
            ResearchPhase.FINAL_REPORT
        ]
    
    def create_interface(self) -> gr.Blocks:
        """创建主界面"""
        with gr.Blocks(
            theme=gr.themes.Soft(),
            title="深度研究工具",
            css=self._get_custom_css()
        ) as app:
            
            # 全局状态管理
            session_id = gr.State(value=str(uuid.uuid4()))
            current_step = gr.State(value=0)
            completed_steps = gr.State(value=[False] * 6)
            step_data = gr.State(value={})
            
            # 标题和说明
            gr.Markdown("""
            # 🔬 AI 深度研究工具
            
            这是一个基于AI的智能研究助手，能够帮助您进行深度的主题研究并生成综合性报告。
            """)
            
            # 步骤指示器
            step_indicator = gr.HTML(
                value=create_step_indicator(0, [False] * 6),
                elem_id="step_indicator"
            )
            
            # 使用Tabs实现步骤切换
            with gr.Tabs() as tabs:
                # 第一步：主题输入
                with gr.Tab("1. 主题输入", id="tab_topic", interactive=True):
                    topic_input, topic_examples, topic_status = create_topic_input_step()
                
                # 第二步：AI思考
                with gr.Tab("2. AI思考", id="tab_thinking", interactive=False):
                    thinking_accordion, thinking_display, thinking_summary, ai_questions, user_answers = create_thinking_step()
                
                # 第三步：研究计划
                with gr.Tab("3. 研究计划", id="tab_planning", interactive=False):
                    research_plan, plan_feedback, serp_queries = create_planning_step()
                
                # 第四步：信息收集（占位）
                with gr.Tab("4. 信息收集", id="tab_collection", interactive=False):
                    gr.Markdown("## 🔍 第四步：信息收集")
                    gr.Markdown("*此步骤将在后续版本中实现*")
                
                # 第五步：深度研究（占位）
                with gr.Tab("5. 深度研究", id="tab_deep_research", interactive=False):
                    gr.Markdown("## 🔬 第五步：深度研究")
                    gr.Markdown("*此步骤将在后续版本中实现*")
                
                # 第六步：生成报告（占位）
                with gr.Tab("6. 生成报告", id="tab_final_report", interactive=False):
                    gr.Markdown("## 📊 第六步：生成最终报告")
                    gr.Markdown("*此步骤将在后续版本中实现*")
            
            # 导航控制
            prev_btn, execute_btn, next_btn, redo_btn, auto_advance, progress_display = create_navigation_controls()
            
            # 状态显示
            status_display = gr.HTML(value="", elem_id="status_display")
            
            # 事件绑定
            self._setup_event_handlers(
                app, session_id, current_step, completed_steps, step_data,
                topic_input, thinking_display, thinking_summary, research_plan,
                prev_btn, execute_btn, next_btn, redo_btn, auto_advance,
                step_indicator, status_display, tabs
            )
        
        return app
    
    def _get_custom_css(self) -> str:
        """获取自定义CSS样式"""
        return """
        .thinking-stream {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-left: 4px solid #4a90e2;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: "Monaco", "Menlo", monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .progress-display {
            text-align: center;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 8px;
            margin: 10px 0;
        }
        """
    
    def _setup_event_handlers(self, app, session_id, current_step, completed_steps, step_data,
                            topic_input, thinking_display, thinking_summary, research_plan,
                            prev_btn, execute_btn, next_btn, redo_btn, auto_advance,
                            step_indicator, status_display, tabs):
        """设置事件处理器"""
        
        # 执行当前步骤
        execute_btn.click(
            fn=self.execute_current_step,
            inputs=[
                current_step, step_data, topic_input,
                thinking_display, research_plan, auto_advance
            ],
            outputs=[
                step_indicator, status_display, thinking_display,
                thinking_summary, research_plan, completed_steps,
                prev_btn, execute_btn, next_btn, redo_btn, tabs
            ]
        )
        
        # 下一步按钮（手动模式）
        next_btn.click(
            fn=self.advance_step,
            inputs=[current_step, completed_steps],
            outputs=[
                current_step, step_indicator, tabs,
                prev_btn, next_btn, redo_btn
            ]
        )
        
        # 上一步按钮
        prev_btn.click(
            fn=self.go_back_step,
            inputs=[current_step, completed_steps],
            outputs=[
                current_step, completed_steps, step_indicator, tabs,
                prev_btn, next_btn, redo_btn, status_display
            ]
        )
    
    async def execute_current_step(self, current_step: int, step_data: dict, topic_input: str,
                                 thinking_display: str, research_plan: str, auto_advance: bool):
        """执行当前步骤"""
        try:
            # 显示执行状态
            status_html = create_success_display("正在执行...")
            
            if current_step == 0:  # 主题输入
                return await self._execute_topic_step(topic_input, step_data, auto_advance)
            elif current_step == 1:  # 思考过程
                return await self._execute_thinking_step(step_data, auto_advance)
            elif current_step == 2:  # 研究计划
                return await self._execute_planning_step(step_data, auto_advance)
            
        except Exception as e:
            logger.error(f"步骤执行失败: {str(e)}")
            error_html = create_error_display(f"执行失败: {str(e)}")
            return [gr.update()] * 11  # 返回空更新
    
    async def _execute_topic_step(self, topic: str, step_data: dict, auto_advance: bool):
        """执行主题输入步骤"""
        if not topic.strip():
            raise ValueError("请输入研究主题")
        
        # 保存主题数据
        step_data["topic"] = topic.strip()
        
        # 更新完成状态
        completed_steps = [True, False, False, False, False, False]
        
        # 更新UI
        updates = [
            gr.update(value=create_step_indicator(0, completed_steps)),  # step_indicator
            gr.update(value=create_success_display("主题已确认")),  # status_display
            gr.update(),  # thinking_display
            gr.update(),  # thinking_summary
            gr.update(),  # research_plan
            gr.update(value=completed_steps),  # completed_steps
            gr.update(interactive=True),  # prev_btn
            gr.update(value="执行当前步骤"),  # execute_btn
            gr.update(interactive=True, visible=not auto_advance),  # next_btn
            gr.update(interactive=True),  # redo_btn
            gr.update(selected="tab_thinking" if auto_advance else "tab_topic")  # tabs
        ]
        
        return updates
    
    async def _execute_thinking_step(self, step_data: dict, auto_advance: bool):
        """执行思考步骤"""
        topic = step_data.get("topic", "")
        if not topic:
            raise ValueError("缺少研究主题")
        
        # 调用AI进行思考
        thinking_result = await self.ai_client.thinking_mode(topic)
        step_data["thinking_result"] = thinking_result
        
        # 更新完成状态
        completed_steps = [True, True, False, False, False, False]
        
        # 更新UI
        updates = [
            gr.update(value=create_step_indicator(1, completed_steps)),  # step_indicator
            gr.update(value=create_success_display("思考完成")),  # status_display
            gr.update(value=thinking_result),  # thinking_display
            gr.update(value="### 思考摘要\n" + thinking_result[:200] + "...", visible=True),  # thinking_summary
            gr.update(),  # research_plan
            gr.update(value=completed_steps),  # completed_steps
            gr.update(interactive=True),  # prev_btn
            gr.update(value="执行当前步骤"),  # execute_btn
            gr.update(interactive=True, visible=not auto_advance),  # next_btn
            gr.update(interactive=True),  # redo_btn
            gr.update(selected="tab_planning" if auto_advance else "tab_thinking")  # tabs
        ]
        
        return updates
    
    async def _execute_planning_step(self, step_data: dict, auto_advance: bool):
        """执行计划步骤"""
        topic = step_data.get("topic", "")
        thinking_result = step_data.get("thinking_result", "")
        
        if not topic or not thinking_result:
            raise ValueError("缺少必要的前置数据")
        
        # 调用AI制定研究计划
        plan_result = await self.ai_client.planning_mode(topic, thinking_result)
        step_data["research_plan"] = plan_result
        
        # 更新完成状态
        completed_steps = [True, True, True, False, False, False]
        
        # 更新UI
        updates = [
            gr.update(value=create_step_indicator(2, completed_steps)),  # step_indicator
            gr.update(value=create_success_display("研究计划已生成")),  # status_display
            gr.update(),  # thinking_display
            gr.update(),  # thinking_summary
            gr.update(value=plan_result),  # research_plan
            gr.update(value=completed_steps),  # completed_steps
            gr.update(interactive=True),  # prev_btn
            gr.update(value="执行当前步骤"),  # execute_btn
            gr.update(interactive=True, visible=not auto_advance),  # next_btn
            gr.update(interactive=True),  # redo_btn
            gr.update(selected="tab_collection" if auto_advance else "tab_planning")  # tabs
        ]
        
        return updates
    
    def advance_step(self, current_step: int, completed_steps: list):
        """前进到下一步"""
        if current_step < 5 and completed_steps[current_step]:
            new_step = current_step + 1
            tab_names = ["tab_topic", "tab_thinking", "tab_planning", "tab_collection", "tab_deep_research", "tab_final_report"]
            
            return [
                gr.update(value=new_step),  # current_step
                gr.update(value=create_step_indicator(new_step, completed_steps)),  # step_indicator
                gr.update(selected=tab_names[new_step]),  # tabs
                gr.update(interactive=new_step > 0),  # prev_btn
                gr.update(interactive=completed_steps[new_step] if new_step < 6 else False),  # next_btn
                gr.update(interactive=completed_steps[new_step] if new_step < 6 else False)  # redo_btn
            ]
        
        return [gr.update()] * 6
    
    def go_back_step(self, current_step: int, completed_steps: list):
        """回退到上一步"""
        if current_step > 0:
            new_step = current_step - 1
            tab_names = ["tab_topic", "tab_thinking", "tab_planning", "tab_collection", "tab_deep_research", "tab_final_report"]
            
            # 清理后续步骤的完成状态
            new_completed = completed_steps.copy()
            for i in range(current_step, len(new_completed)):
                new_completed[i] = False
            
            return [
                gr.update(value=new_step),  # current_step
                gr.update(value=new_completed),  # completed_steps
                gr.update(value=create_step_indicator(new_step, new_completed)),  # step_indicator
                gr.update(selected=tab_names[new_step]),  # tabs
                gr.update(interactive=new_step > 0),  # prev_btn
                gr.update(interactive=False),  # next_btn
                gr.update(interactive=new_completed[new_step]),  # redo_btn
                gr.update(value="")  # status_display
            ]
        
        return [gr.update()] * 8


def create_app() -> gr.Blocks:
    """创建应用实例"""
    interface = ResearchInterface()
    return interface.create_interface()
